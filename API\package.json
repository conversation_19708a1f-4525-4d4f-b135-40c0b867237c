{"name": "ionalumni", "version": "1.0.0", "description": "", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "start:prod": "NODE_ENV=production node dist/server.js", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "postbuild": "npm run db:generate", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:reset": "prisma migrate reset", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "test": "echo \"Tests not configured yet\"", "test:watch": "echo \"Test watch not configured yet\"", "type-check": "tsc --noEmit"}, "repository": {"type": "git", "url": "git+https://github.com/sushmitkumarpatil/IonAlumni.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/sush<PERSON><PERSON><PERSON><PERSON>/IonAlumni/issues"}, "homepage": "https://github.com/sushmitkumarpatil/IonAlumni#readme", "dependencies": {"@prisma/client": "^6.12.0", "@types/bull": "^3.15.9", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "nodemailer": "^7.0.5", "winston": "^3.17.0", "ws": "^8.18.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.15", "@types/nodemailer": "^6.4.17", "@types/ws": "^8.18.1", "nodemon": "^3.1.10", "prisma": "^6.12.0", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}