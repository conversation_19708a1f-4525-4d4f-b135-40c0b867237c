import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
import { prisma } from "../config/database";
import { createError } from "../middleware/errorHandler";

interface UpdateProfileRequest {
  name?: string;
  bio?: string;
  mobile?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
  company?: string;
  jobTitle?: string;
  experience?: number;
  location?: string;
  showEmail?: boolean;
  showMobile?: boolean;
  showLinkedin?: boolean;
}

interface ConnectionRequestBody {
  receiverId: string;
  message?: string;
}

interface ConnectionResponseBody {
  status: "ACCEPTED" | "REJECTED" | "BLOCKED";
}

/**
 * Get current user's profile
 */
export const getProfile = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        id: true,
        email: true,
        name: true,
        mobile: true,
        usn: true,
        course: true,
        batch: true,
        role: true,
        status: true,
        profilePicture: true,
        bio: true,
        linkedinUrl: true,
        githubUrl: true,
        portfolioUrl: true,
        company: true,
        jobTitle: true,
        experience: true,
        location: true,
        showEmail: true,
        showMobile: true,
        showLinkedin: true,
        createdAt: true,
        lastLoginAt: true,
        _count: {
          select: {
            jobsPosted: true,
            eventsCreated: true,
            posts: true,
            connections: {
              where: { status: "ACCEPTED" },
            },
          },
        },
      },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    res.json({
      user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update current user's profile
 */
export const updateProfile = async (req: Request<{}, {}, UpdateProfileRequest>, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const updateData = req.body;

    // Remove undefined values
    const cleanedData = Object.fromEntries(Object.entries(updateData).filter(([_, value]) => value !== undefined));

    const updatedUser = await prisma.user.update({
      where: { id: req.user.userId },
      data: cleanedData,
      select: {
        id: true,
        email: true,
        name: true,
        mobile: true,
        usn: true,
        course: true,
        batch: true,
        role: true,
        status: true,
        profilePicture: true,
        bio: true,
        linkedinUrl: true,
        githubUrl: true,
        portfolioUrl: true,
        company: true,
        jobTitle: true,
        experience: true,
        location: true,
        showEmail: true,
        showMobile: true,
        showLinkedin: true,
        updatedAt: true,
      },
    });

    res.json({
      message: "Profile updated successfully",
      user: updatedUser,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user directory with search and filtering
 */
export const getUserDirectory = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const search = req.query.search as string;
    const role = req.query.role as UserRole;
    const course = req.query.course as string;
    const batch = req.query.batch as string;
    const company = req.query.company as string;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      status: UserStatus.APPROVED,
      NOT: {
        id: req.user?.userId, // Exclude current user
      },
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { company: { contains: search, mode: "insensitive" } },
        { jobTitle: { contains: search, mode: "insensitive" } },
        { location: { contains: search, mode: "insensitive" } },
      ];
    }

    if (role) {
      where.role = role;
    }

    if (course) {
      where.course = { contains: course, mode: "insensitive" };
    }

    if (batch) {
      where.batch = batch;
    }

    if (company) {
      where.company = { contains: company, mode: "insensitive" };
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          name: true,
          email: true,
          mobile: true,
          course: true,
          batch: true,
          role: true,
          profilePicture: true,
          bio: true,
          linkedinUrl: true,
          company: true,
          jobTitle: true,
          experience: true,
          location: true,
          showEmail: true,
          showMobile: true,
          showLinkedin: true,
        },
        skip,
        take: limit,
        orderBy: [
          { role: "asc" }, // Alumni first
          { name: "asc" },
        ],
      }),
      prisma.user.count({ where }),
    ]);

    // Filter contact information based on privacy settings
    const filteredUsers = users.map((user) => ({
      ...user,
      email: user.showEmail ? user.email : null,
      mobile: user.showMobile ? user.mobile : null,
      linkedinUrl: user.showLinkedin ? user.linkedinUrl : null,
    }));

    res.json({
      users: filteredUsers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user by ID
 */
export const getUserById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    if (!id) {
      throw createError("User ID is required", 400);
    }

    const user = await prisma.user.findUnique({
      where: {
        id,
        status: UserStatus.APPROVED,
      },
      select: {
        id: true,
        name: true,
        email: true,
        mobile: true,
        course: true,
        batch: true,
        role: true,
        profilePicture: true,
        bio: true,
        linkedinUrl: true,
        githubUrl: true,
        portfolioUrl: true,
        company: true,
        jobTitle: true,
        experience: true,
        location: true,
        showEmail: true,
        showMobile: true,
        showLinkedin: true,
        createdAt: true,
        _count: {
          select: {
            jobsPosted: {
              where: { isActive: true },
            },
            eventsCreated: {
              where: { isActive: true },
            },
            posts: true,
          },
        },
      },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    // Check if current user is connected to this user
    let connectionStatus = null;
    if (req.user && req.user.userId !== id) {
      const connection = await prisma.connection.findFirst({
        where: {
          OR: [
            { requesterId: req.user.userId, receiverId: id },
            { requesterId: id, receiverId: req.user.userId },
          ],
        },
        select: {
          status: true,
          requesterId: true,
        },
      });

      if (connection) {
        connectionStatus = {
          status: connection.status,
          isRequester: connection.requesterId === req.user.userId,
        };
      }
    }

    // Filter contact information based on privacy settings
    const filteredUser = {
      ...user,
      email: user.showEmail ? user.email : null,
      mobile: user.showMobile ? user.mobile : null,
      linkedinUrl: user.showLinkedin ? user.linkedinUrl : null,
      connectionStatus,
    };

    res.json({
      user: filteredUser,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user connections
 */
export const getConnections = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const [connections, total] = await Promise.all([
      prisma.connection.findMany({
        where: {
          OR: [{ requesterId: req.user.userId }, { receiverId: req.user.userId }],
          status: "ACCEPTED",
        },
        include: {
          requester: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              role: true,
              company: true,
              jobTitle: true,
              course: true,
              batch: true,
            },
          },
          receiver: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              role: true,
              company: true,
              jobTitle: true,
              course: true,
              batch: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { updatedAt: "desc" },
      }),
      prisma.connection.count({
        where: {
          OR: [{ requesterId: req.user.userId }, { receiverId: req.user.userId }],
          status: "ACCEPTED",
        },
      }),
    ]);

    // Format connections to show the other user
    const formattedConnections = connections.map((connection) => {
      const otherUser = connection.requesterId === req.user!.userId ? connection.receiver : connection.requester;

      return {
        id: connection.id,
        user: otherUser,
        connectedAt: connection.updatedAt,
      };
    });

    res.json({
      connections: formattedConnections,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Send connection request
 */
export const sendConnectionRequest = async (
  req: Request<{}, {}, ConnectionRequestBody>,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { receiverId, message } = req.body;

    if (req.user.userId === receiverId) {
      throw createError("Cannot send connection request to yourself", 400);
    }

    // Check if receiver exists and is approved
    const receiver = await prisma.user.findUnique({
      where: {
        id: receiverId,
        status: UserStatus.APPROVED,
      },
    });

    if (!receiver) {
      throw createError("User not found", 404);
    }

    // Check if connection already exists
    const existingConnection = await prisma.connection.findFirst({
      where: {
        OR: [
          { requesterId: req.user.userId, receiverId },
          { requesterId: receiverId, receiverId: req.user.userId },
        ],
      },
    });

    if (existingConnection) {
      if (existingConnection.status === "ACCEPTED") {
        throw createError("Already connected with this user", 409);
      }
      if (existingConnection.status === "PENDING") {
        throw createError("Connection request already sent", 409);
      }
      if (existingConnection.status === "BLOCKED") {
        throw createError("Cannot send connection request to this user", 403);
      }
    }

    // Create connection request
    const connection = await prisma.connection.create({
      data: {
        requesterId: req.user.userId,
        receiverId,
        message: message ?? null,
        status: "PENDING",
      },
      include: {
        receiver: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            role: true,
          },
        },
      },
    });

    // Create notification for receiver
    await prisma.notification.create({
      data: {
        userId: receiverId,
        type: "CONNECTION_REQUEST",
        title: "New Connection Request",
        message: `${req.user.email} sent you a connection request`,
        data: {
          connectionId: connection.id,
          requesterId: req.user.userId,
        },
      },
    });

    res.status(201).json({
      message: "Connection request sent successfully",
      connection: {
        id: connection.id,
        receiver: connection.receiver,
        message: connection.message,
        status: connection.status,
        createdAt: connection.createdAt,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Respond to connection request
 */
export const respondToConnection = async (
  req: Request<{ id: string }, {}, ConnectionResponseBody>,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const { id } = req.params;
    const { status } = req.body;

    // Find connection request
    const connection = await prisma.connection.findUnique({
      where: { id },
      include: {
        requester: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!connection) {
      throw createError("Connection request not found", 404);
    }

    if (connection.receiverId !== req.user.userId) {
      throw createError("Not authorized to respond to this request", 403);
    }

    if (connection.status !== "PENDING") {
      throw createError("Connection request has already been responded to", 409);
    }

    // Update connection status
    const updatedConnection = await prisma.connection.update({
      where: { id },
      data: { status },
      include: {
        requester: {
          select: {
            id: true,
            name: true,
            profilePicture: true,
            role: true,
          },
        },
      },
    });

    // Create notification for requester
    let notificationMessage = "";
    switch (status) {
      case "ACCEPTED":
        notificationMessage = `${req.user.email} accepted your connection request`;
        break;
      case "REJECTED":
        notificationMessage = `${req.user.email} declined your connection request`;
        break;
      case "BLOCKED":
        notificationMessage = `${req.user.email} blocked your connection request`;
        break;
    }

    await prisma.notification.create({
      data: {
        userId: connection.requesterId,
        type: "CONNECTION_REQUEST",
        title: "Connection Request Response",
        message: notificationMessage,
        data: {
          connectionId: connection.id,
          status,
        },
      },
    });

    res.json({
      message: `Connection request ${status.toLowerCase()} successfully`,
      connection: {
        id: updatedConnection.id,
        requester: updatedConnection.requester,
        status: updatedConnection.status,
        updatedAt: updatedConnection.updatedAt,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get connection requests (received)
 */
export const getConnectionRequests = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const skip = (page - 1) * limit;

    const [requests, total] = await Promise.all([
      prisma.connection.findMany({
        where: {
          receiverId: req.user.userId,
          status: "PENDING",
        },
        include: {
          requester: {
            select: {
              id: true,
              name: true,
              profilePicture: true,
              role: true,
              company: true,
              jobTitle: true,
              course: true,
              batch: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.connection.count({
        where: {
          receiverId: req.user.userId,
          status: "PENDING",
        },
      }),
    ]);

    res.json({
      requests,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};
