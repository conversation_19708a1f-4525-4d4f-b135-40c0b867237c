import rateLimit from "express-rate-limit";
import { redis, CacheService, Cache<PERSON>eys } from "../config/redis";
import { Logger } from "../services/loggerService";

// Redis store for rate limiting (distributed rate limiting)
const RedisStore = require("rate-limit-redis");

// Create Redis store for rate limiting
const createRedisStore = () => {
  try {
    return new RedisStore({
      sendCommand: (...args: string[]) => {
        const [command, ...commandArgs] = args;
        if (!command) {
          throw new Error("Redis command is required");
        }
        return redis.call(command, ...commandArgs);
      },
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    Logger.warn("Redis store not available, falling back to memory store", { error: errorMessage });
    return undefined;
  }
};

// General rate limiter
export const rateLimiter = rateLimit({
  store: createRedisStore(),
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "100"), // limit each IP to 100 requests per windowMs
  message: {
    error: "Too many requests from this IP, please try again later.",
    retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000") / 1000),
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  handler: (req, res) => {
    Logger.security("Rate limit exceeded", req.ip, {
      userAgent: req.get("User-Agent"),
      endpoint: req.path,
      method: req.method,
    });

    res.status(429).json({
      error: "Too many requests from this IP, please try again later.",
      retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000") / 1000),
    });
  },
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === "/health";
  },
});

// Stricter rate limiting for auth endpoints
export const authRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs for auth
  message: {
    error: "Too many authentication attempts, please try again later.",
    retryAfter: 900, // 15 minutes in seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (_req, res) => {
    res.status(429).json({
      error: "Too many authentication attempts, please try again later.",
      retryAfter: 900,
    });
  },
});
