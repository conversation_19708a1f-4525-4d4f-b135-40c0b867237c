import Bull from "bull";
import { sendEmail } from "./emailService";
import { createNotification } from "./notificationService";
import { uploadToCloudinary } from "./cloudinaryService";
import { Logger } from "./loggerService";

// Enhanced queue configuration with scaling support
const queueConfig = {
  redis: {
    host: process.env.REDIS_HOST || "localhost",
    port: parseInt(process.env.REDIS_PORT || "6379"),
    ...(process.env.REDIS_PASSWORD && { password: process.env.REDIS_PASSWORD }),
    db: parseInt(process.env.REDIS_DB || "0"),
  },
  defaultJobOptions: {
    removeOnComplete: parseInt(process.env.QUEUE_KEEP_COMPLETED || "50"),
    removeOnFail: parseInt(process.env.QUEUE_KEEP_FAILED || "100"),
    attempts: parseInt(process.env.QUEUE_MAX_ATTEMPTS || "3"),
    backoff: {
      type: "exponential",
      delay: parseInt(process.env.QUEUE_BACKOFF_DELAY || "2000"),
    },
  },
  settings: {
    stalledInterval: parseInt(process.env.QUEUE_STALLED_INTERVAL || "30000"),
    maxStalledCount: parseInt(process.env.QUEUE_MAX_STALLED || "1"),
  },
};

// Create queues with enhanced configuration
export const emailQueue = new Bull("email processing", {
  ...queueConfig,
  settings: {
    ...queueConfig.settings,
    retryProcessDelay: 5000,
  },
});

export const notificationQueue = new Bull("notification processing", {
  ...queueConfig,
  settings: {
    ...queueConfig.settings,
    retryProcessDelay: 3000,
  },
});

export const fileProcessingQueue = new Bull("file processing", {
  ...queueConfig,
  settings: {
    ...queueConfig.settings,
    retryProcessDelay: 10000,
  },
});

export const analyticsQueue = new Bull("analytics processing", {
  ...queueConfig,
  settings: {
    ...queueConfig.settings,
    retryProcessDelay: 15000,
  },
});

// Queue monitoring and metrics
type QueueName = "emailQueue" | "notificationQueue" | "fileProcessingQueue" | "analyticsQueue";

const queueMetrics: Record<QueueName, { processed: number; failed: number; active: number }> = {
  emailQueue: { processed: 0, failed: 0, active: 0 },
  notificationQueue: { processed: 0, failed: 0, active: 0 },
  fileProcessingQueue: { processed: 0, failed: 0, active: 0 },
  analyticsQueue: { processed: 0, failed: 0, active: 0 },
};

// Setup queue event listeners for monitoring
const setupQueueMonitoring = (queue: Bull.Queue, queueName: QueueName) => {
  queue.on("completed", (job) => {
    queueMetrics[queueName].processed++;
    Logger.queue(`Job completed in ${queueName}`, queueName, String(job.id), {
      duration: job.processedOn ? Date.now() - job.processedOn : 0,
      attempts: job.attemptsMade,
    });
  });

  queue.on("failed", (job, err) => {
    queueMetrics[queueName].failed++;
    Logger.error(`Job failed in ${queueName}`, {
      queueName,
      jobId: String(job.id),
      error: err.message,
      attempts: job.attemptsMade,
      data: job.data,
    });
  });

  queue.on("active", (job) => {
    queueMetrics[queueName].active++;
    Logger.queue(`Job started in ${queueName}`, queueName, String(job.id));
  });

  queue.on("stalled", (job) => {
    Logger.warn(`Job stalled in ${queueName}`, {
      queueName,
      jobId: String(job.id),
      data: job.data,
    });
  });

  queue.on("progress", (job, progress) => {
    Logger.queue(`Job progress in ${queueName}: ${progress}%`, queueName, String(job.id));
  });
};

// Initialize monitoring for all queues
setupQueueMonitoring(emailQueue, "emailQueue");
setupQueueMonitoring(notificationQueue, "notificationQueue");
setupQueueMonitoring(fileProcessingQueue, "fileProcessingQueue");
setupQueueMonitoring(analyticsQueue, "analyticsQueue");

// Email queue processors
emailQueue.process("send-email", async (job) => {
  const { to, subject, html, text } = job.data;

  try {
    await sendEmail({ to, subject, html, text });
    console.log(`✅ Email sent successfully to ${to}`);
    return { success: true, recipient: to };
  } catch (error) {
    console.error(`❌ Failed to send email to ${to}:`, error);
    throw error;
  }
});

emailQueue.process("send-bulk-email", async (job) => {
  const { recipients, subject, html, text } = job.data;
  const results = [];

  for (const recipient of recipients) {
    try {
      await sendEmail({ to: recipient, subject, html, text });
      results.push({ recipient, success: true });
      console.log(`✅ Bulk email sent to ${recipient}`);
    } catch (error) {
      results.push({ recipient, success: false, error: error instanceof Error ? error.message : String(error) });
      console.error(`❌ Failed to send bulk email to ${recipient}:`, error);
    }

    // Add small delay between emails to avoid rate limiting
    await new Promise((resolve) => setTimeout(resolve, 100));
  }

  return results;
});

// Notification queue processors
notificationQueue.process("send-notification", async (job) => {
  const notificationData = job.data;

  try {
    const notification = await createNotification(notificationData);
    console.log(`✅ Notification created: ${notification.id}`);
    return { success: true, notificationId: notification.id };
  } catch (error) {
    console.error("❌ Failed to create notification:", error);
    throw error;
  }
});

notificationQueue.process("send-bulk-notifications", async (job) => {
  const { notifications } = job.data;
  const results = [];

  for (const notificationData of notifications) {
    try {
      const notification = await createNotification(notificationData);
      results.push({ success: true, notificationId: notification.id });
      console.log(`✅ Bulk notification created: ${notification.id}`);
    } catch (error: any) {
      results.push({ success: false, error: error instanceof Error ? error.message : String(error) });
      console.error("❌ Failed to create bulk notification:", error);
    }
  }

  return results;
});

// File processing queue processors
fileProcessingQueue.process("optimize-image", async (job) => {
  const { fileBuffer, options, userId } = job.data;

  try {
    const result = await uploadToCloudinary(fileBuffer, options);
    console.log(`✅ Image optimized for user ${userId}`);
    return result;
  } catch (error) {
    console.error(`❌ Failed to optimize image for user ${userId}:`, error);
    throw error;
  }
});

// Analytics queue processors
analyticsQueue.process("track-event", async (job) => {
  const { eventType, userId, data } = job.data;

  try {
    // Here you would implement your analytics tracking
    // For example, sending to Google Analytics, Mixpanel, etc.
    console.log(`📊 Analytics event tracked: ${eventType} for user ${userId}`);
    return { success: true, eventType, userId };
  } catch (error) {
    console.error(`❌ Failed to track analytics event:`, error);
    throw error;
  }
});

// Queue utility functions
export class QueueService {
  // Add email to queue
  static async sendEmail(
    emailData: {
      to: string;
      subject: string;
      html: string;
      text?: string;
    },
    priority: number = 0
  ) {
    return emailQueue.add("send-email", emailData, {
      priority,
      delay: 0,
    });
  }

  // Add bulk email to queue
  static async sendBulkEmail(
    emailData: {
      recipients: string[];
      subject: string;
      html: string;
      text?: string;
    },
    priority: number = -5
  ) {
    return emailQueue.add("send-bulk-email", emailData, {
      priority,
      delay: 0,
    });
  }

  // Add notification to queue
  static async sendNotification(notificationData: any, priority: number = 0) {
    return notificationQueue.add("send-notification", notificationData, {
      priority,
      delay: 0,
    });
  }

  // Add bulk notifications to queue
  static async sendBulkNotifications(notifications: any[], priority: number = -5) {
    return notificationQueue.add(
      "send-bulk-notifications",
      { notifications },
      {
        priority,
        delay: 0,
      }
    );
  }

  // Add file processing to queue
  static async processFile(
    fileData: {
      fileBuffer: Buffer;
      options: any;
      userId: string;
    },
    priority: number = 0
  ) {
    return fileProcessingQueue.add("optimize-image", fileData, {
      priority,
      delay: 0,
    });
  }

  // Add analytics event to queue
  static async trackEvent(
    eventData: {
      eventType: string;
      userId: string;
      data: any;
    },
    priority: number = -10
  ) {
    return analyticsQueue.add("track-event", eventData, {
      priority,
      delay: 0,
    });
  }

  // Get queue statistics
  static async getQueueStats() {
    const [emailStats, notificationStats, fileStats, analyticsStats] = await Promise.all([
      emailQueue.getJobCounts(),
      notificationQueue.getJobCounts(),
      fileProcessingQueue.getJobCounts(),
      analyticsQueue.getJobCounts(),
    ]);

    return {
      email: emailStats,
      notifications: notificationStats,
      fileProcessing: fileStats,
      analytics: analyticsStats,
      timestamp: new Date().toISOString(),
    };
  }

  // Clean up completed jobs
  static async cleanupQueues() {
    await Promise.all([
      emailQueue.clean(24 * 60 * 60 * 1000, "completed"), // Clean completed jobs older than 24 hours
      emailQueue.clean(7 * 24 * 60 * 60 * 1000, "failed"), // Clean failed jobs older than 7 days
      notificationQueue.clean(24 * 60 * 60 * 1000, "completed"),
      notificationQueue.clean(7 * 24 * 60 * 60 * 1000, "failed"),
      fileProcessingQueue.clean(24 * 60 * 60 * 1000, "completed"),
      fileProcessingQueue.clean(7 * 24 * 60 * 60 * 1000, "failed"),
      analyticsQueue.clean(24 * 60 * 60 * 1000, "completed"),
      analyticsQueue.clean(7 * 24 * 60 * 60 * 1000, "failed"),
    ]);

    console.log("✅ Queue cleanup completed");
  }
}

// Queue event handlers
const setupQueueEventHandlers = (queue: Bull.Queue, queueName: string) => {
  queue.on("completed", (job) => {
    console.log(`✅ ${queueName} job ${job.id} completed`);
  });

  queue.on("failed", (job, err) => {
    console.error(`❌ ${queueName} job ${job.id} failed:`, err);
  });

  queue.on("stalled", (job) => {
    console.warn(`⚠️ ${queueName} job ${job.id} stalled`);
  });
};

// Setup event handlers for all queues
setupQueueEventHandlers(emailQueue, "Email");
setupQueueEventHandlers(notificationQueue, "Notification");
setupQueueEventHandlers(fileProcessingQueue, "File Processing");
setupQueueEventHandlers(analyticsQueue, "Analytics");

// Schedule periodic cleanup
setInterval(() => {
  QueueService.cleanupQueues().catch(console.error);
}, 60 * 60 * 1000); // Run every hour

// Graceful shutdown
const gracefulQueueShutdown = async (signal: string) => {
  console.log(`Received ${signal}, closing queues...`);

  try {
    await Promise.all([
      emailQueue.close(),
      notificationQueue.close(),
      fileProcessingQueue.close(),
      analyticsQueue.close(),
    ]);
    console.log("All queues closed successfully");
  } catch (error) {
    console.error("Error during queue shutdown:", error);
  }
};

process.on("SIGTERM", () => gracefulQueueShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulQueueShutdown("SIGINT"));

// Advanced Queue Management and Monitoring
export class AdvancedQueueService {
  // Get comprehensive queue statistics
  static async getQueueStats() {
    const queues = [
      { name: "emailQueue", queue: emailQueue },
      { name: "notificationQueue", queue: notificationQueue },
      { name: "fileProcessingQueue", queue: fileProcessingQueue },
      { name: "analyticsQueue", queue: analyticsQueue },
    ];

    const stats = await Promise.all(
      queues.map(async ({ name, queue }) => {
        const [waiting, active, completed, failed, delayed] = await Promise.all([
          queue.getWaiting(),
          queue.getActive(),
          queue.getCompleted(),
          queue.getFailed(),
          queue.getDelayed(),
        ]);

        return {
          name,
          counts: {
            waiting: waiting.length,
            active: active.length,
            completed: completed.length,
            failed: failed.length,
            delayed: delayed.length,
          },
          metrics: queueMetrics[name as QueueName],
        };
      })
    );

    return {
      queues: stats,
      timestamp: new Date().toISOString(),
      totalJobs: stats.reduce((sum, queue) => sum + Object.values(queue.counts).reduce((a, b) => a + b, 0), 0),
    };
  }

  // Health check for all queues
  static async getQueueHealth() {
    const queues = [
      { name: "emailQueue", queue: emailQueue },
      { name: "notificationQueue", queue: notificationQueue },
      { name: "fileProcessingQueue", queue: fileProcessingQueue },
      { name: "analyticsQueue", queue: analyticsQueue },
    ];

    const healthChecks = await Promise.all(
      queues.map(async ({ name, queue }) => {
        try {
          const isReady = await queue.isReady();
          const [waiting, active, failed] = await Promise.all([
            queue.getWaiting(),
            queue.getActive(),
            queue.getFailed(),
          ]);

          const failedRecently = failed.filter(
            (job) => job.finishedOn && Date.now() - job.finishedOn < 300000 // 5 minutes
          ).length;

          const status = !isReady
            ? "unhealthy"
            : failedRecently > 10
            ? "degraded"
            : active.length > 100
            ? "degraded"
            : "healthy";

          return {
            name,
            status,
            isReady,
            details: {
              waiting: waiting.length,
              active: active.length,
              failedRecently,
            },
          };
        } catch (error) {
          return {
            name,
            status: "unhealthy",
            isReady: false,
            error: error instanceof Error ? error.message : String(error),
          };
        }
      })
    );

    const overallStatus = healthChecks.every((q) => q.status === "healthy")
      ? "healthy"
      : healthChecks.some((q) => q.status === "unhealthy")
      ? "unhealthy"
      : "degraded";

    return {
      status: overallStatus,
      queues: healthChecks,
      timestamp: new Date().toISOString(),
    };
  }

  // Clean up completed and failed jobs
  static async cleanupJobs(olderThanHours: number = 24) {
    const cutoffTime = Date.now() - olderThanHours * 60 * 60 * 1000;
    const queues = [emailQueue, notificationQueue, fileProcessingQueue, analyticsQueue];

    const results = await Promise.all(
      queues.map(async (queue) => {
        try {
          const [completed, failed] = await Promise.all([queue.getCompleted(), queue.getFailed()]);

          const oldCompleted = completed.filter((job) => job.finishedOn && job.finishedOn < cutoffTime);
          const oldFailed = failed.filter((job) => job.finishedOn && job.finishedOn < cutoffTime);

          await Promise.all([...oldCompleted.map((job) => job.remove()), ...oldFailed.map((job) => job.remove())]);

          return {
            queue: queue.name,
            cleaned: {
              completed: oldCompleted.length,
              failed: oldFailed.length,
            },
          };
        } catch (error) {
          Logger.error(`Failed to cleanup queue ${queue.name}`, error);
          return {
            queue: queue.name,
            error: error instanceof Error ? error.message : String(error),
          };
        }
      })
    );

    Logger.info("Queue cleanup completed", { results, olderThanHours });
    return results;
  }

  // Pause/Resume queue operations
  static async pauseQueue(queueName: QueueName) {
    const queueMap = {
      emailQueue,
      notificationQueue,
      fileProcessingQueue,
      analyticsQueue,
    };

    const queue = queueMap[queueName];
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    await queue.pause();
    Logger.info(`Queue ${queueName} paused`);
    return { success: true, message: `Queue ${queueName} paused` };
  }

  static async resumeQueue(queueName: QueueName) {
    const queueMap = {
      emailQueue,
      notificationQueue,
      fileProcessingQueue,
      analyticsQueue,
    };

    const queue = queueMap[queueName];
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    await queue.resume();
    Logger.info(`Queue ${queueName} resumed`);
    return { success: true, message: `Queue ${queueName} resumed` };
  }

  // Retry failed jobs
  static async retryFailedJobs(queueName: QueueName, limit: number = 10) {
    const queueMap = {
      emailQueue,
      notificationQueue,
      fileProcessingQueue,
      analyticsQueue,
    };

    const queue = queueMap[queueName];
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const failedJobs = await queue.getFailed();
    const jobsToRetry = failedJobs.slice(0, limit);

    const results = await Promise.all(
      jobsToRetry.map(async (job) => {
        try {
          await job.retry();
          return { jobId: job.id, success: true };
        } catch (error) {
          return {
            jobId: job.id,
            success: false,
            error: error instanceof Error ? error.message : String(error),
          };
        }
      })
    );

    Logger.info(`Retried ${jobsToRetry.length} failed jobs in ${queueName}`, { results });
    return results;
  }

  // Get queue performance metrics
  static getPerformanceMetrics() {
    return {
      metrics: queueMetrics,
      timestamp: new Date().toISOString(),
      summary: {
        totalProcessed: Object.values(queueMetrics).reduce((sum, queue) => sum + queue.processed, 0),
        totalFailed: Object.values(queueMetrics).reduce((sum, queue) => sum + queue.failed, 0),
        totalActive: Object.values(queueMetrics).reduce((sum, queue) => sum + queue.active, 0),
      },
    };
  }
}

export default QueueService;
