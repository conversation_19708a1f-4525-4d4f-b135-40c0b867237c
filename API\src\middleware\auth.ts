import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
import { AuthUtils } from "../utils/auth";
import { prisma } from "../config/database";
import { createError } from "./errorHandler";

// Note: Request interface extension is defined in src/types/express.d.ts

/**
 * Middleware to authenticate JWT token
 */
export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = AuthUtils.extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      throw createError("Access token is required", 401);
    }

    const payload = AuthUtils.verifyAccessToken(token);

    // Verify user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        role: true,
        status: true,
        name: true,
      },
    });

    if (!user) {
      throw createError("User not found", 401);
    }

    if (user.status === UserStatus.SUSPENDED) {
      throw createError("Account has been suspended", 403);
    }

    if (user.status === UserStatus.REJECTED) {
      throw createError("Account access has been denied", 403);
    }

    // Add user info to request
    req.user = {
      userId: payload.userId,
      email: user.email,
      role: user.role,
      status: user.status,
      id: user.id,
    };

    next();
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === "JsonWebTokenError") {
        return next(createError("Invalid token", 401));
      }
      if (error.name === "TokenExpiredError") {
        return next(createError("Token expired", 401));
      }
    }
    next(error);
  }
};

/**
 * Middleware to check if user is approved
 */
export const requireApproved = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return next(createError("Authentication required", 401));
  }

  if (req.user.status !== UserStatus.APPROVED) {
    return next(createError("Account pending approval", 403));
  }

  next();
};

/**
 * Middleware to authorize specific roles
 */
export const authorize = (...roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createError("Authentication required", 401));
    }

    if (!roles.includes(req.user.role as UserRole)) {
      return next(createError("Insufficient permissions", 403));
    }

    next();
  };
};

/**
 * Middleware to check if user is admin
 */
export const requireAdmin = authorize(UserRole.ADMIN);

/**
 * Middleware to check if user is alumni
 */
export const requireAlumni = authorize(UserRole.ALUMNI);

/**
 * Middleware to check if user is student
 */
export const requireStudent = authorize(UserRole.STUDENT);

/**
 * Middleware to check if user is alumni or admin
 */
export const requireAlumniOrAdmin = authorize(UserRole.ALUMNI, UserRole.ADMIN);

/**
 * Middleware to check if user is student or alumni (not admin-only)
 */
export const requireStudentOrAlumni = authorize(UserRole.STUDENT, UserRole.ALUMNI);

/**
 * Optional authentication - doesn't fail if no token provided
 */
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = AuthUtils.extractTokenFromHeader(req.headers.authorization);

    if (token) {
      const payload = AuthUtils.verifyAccessToken(token);

      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: {
          id: true,
          email: true,
          role: true,
          status: true,
          name: true,
        },
      });

      if (user && user.status === UserStatus.APPROVED) {
        req.user = {
          userId: payload.userId,
          email: user.email,
          role: user.role,
          status: user.status,
          id: user.id,
        };
      }
    }
  } catch (error) {
    // Ignore authentication errors for optional auth
  }

  next();
};
