import { Request, Response, NextFunction } from "express";
import { MetricsService } from "../services/metricsService";
import { Logger } from "../services/loggerService";

// Performance monitoring middleware
export const performanceMonitoring = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    const startHrTime = process.hrtime.bigint();

    // Store original end function
    const originalEnd = res.end.bind(res);

    res.end = function (chunk?: any, encoding?: any) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      const hrDuration = Number(process.hrtime.bigint() - startHrTime) / 1000000; // Convert to ms

      // Record request metrics
      MetricsService.recordRequest({
        method: req.method,
        path: req.route?.path || req.path,
        statusCode: res.statusCode,
        duration: hrDuration,
        userAgent: req.get("User-Agent") ?? "",
        ip: req.ip ?? "",
        userId: req.user?.userId ?? "",
      });

      // Log slow requests
      if (duration > 1000) {
        Logger.warn(`Slow request detected: ${req.method} ${req.path}`, {
          duration: `${duration}ms`,
          statusCode: res.statusCode,
          userAgent: req.get("User-Agent"),
          ip: req.ip,
          userId: req.user?.userId,
        });
      }

      // Log errors
      if (res.statusCode >= 400) {
        Logger.error(`HTTP Error: ${req.method} ${req.path}`, {
          statusCode: res.statusCode,
          duration: `${duration}ms`,
          userAgent: req.get("User-Agent"),
          ip: req.ip,
          userId: req.user?.userId,
        });
      }

      return originalEnd(chunk, encoding);
    };

    next();
  };
};

// Database query performance monitoring
export const withDatabasePerformanceMonitoring = async <T>(
  operation: string,
  queryFn: () => Promise<T>
): Promise<T> => {
  const startTime = Date.now();

  try {
    const result = await queryFn();
    const duration = Date.now() - startTime;

    // Record database operation metric
    MetricsService.recordMetric("database_query_duration", duration, "ms", {
      operation,
    });

    // Log slow queries
    if (duration > 1000) {
      Logger.warn(`Slow database query: ${operation}`, {
        duration: `${duration}ms`,
      });
    }

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;

    // Record failed database operation
    MetricsService.recordMetric("database_query_errors", 1, "count", {
      operation,
    });

    Logger.error(`Database query failed: ${operation}`, {
      error: error instanceof Error ? error.message : String(error),
      duration: `${duration}ms`,
    });

    throw error;
  }
};

// Cache performance monitoring
export const withCachePerformanceMonitoring = async <T>(operation: string, cacheFn: () => Promise<T>): Promise<T> => {
  const startTime = Date.now();

  try {
    const result = await cacheFn();
    const duration = Date.now() - startTime;

    // Record cache operation metric
    MetricsService.recordMetric("cache_operation_duration", duration, "ms", {
      operation,
    });

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;

    // Record failed cache operation
    MetricsService.recordMetric("cache_operation_errors", 1, "count", {
      operation,
    });

    Logger.error(`Cache operation failed: ${operation}`, {
      error: error instanceof Error ? error.message : String(error),
      duration: `${duration}ms`,
    });

    throw error;
  }
};

// External API call performance monitoring
export const withExternalApiPerformanceMonitoring = async <T>(
  serviceName: string,
  endpoint: string,
  apiFn: () => Promise<T>
): Promise<T> => {
  const startTime = Date.now();

  try {
    const result = await apiFn();
    const duration = Date.now() - startTime;

    // Record external API call metric
    MetricsService.recordMetric("external_api_duration", duration, "ms", {
      service: serviceName,
      endpoint,
      status: "success",
    });

    // Log slow external API calls
    if (duration > 5000) {
      Logger.warn(`Slow external API call: ${serviceName}/${endpoint}`, {
        duration: `${duration}ms`,
      });
    }

    return result;
  } catch (error) {
    const duration = Date.now() - startTime;

    // Record failed external API call
    MetricsService.recordMetric("external_api_duration", duration, "ms", {
      service: serviceName,
      endpoint,
      status: "error",
    });

    MetricsService.recordMetric("external_api_errors", 1, "count", {
      service: serviceName,
      endpoint,
    });

    Logger.error(`External API call failed: ${serviceName}/${endpoint}`, {
      error: error instanceof Error ? error.message : String(error),
      duration: `${duration}ms`,
    });

    throw error;
  }
};

// Memory usage monitoring middleware
export const memoryMonitoring = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    const memBefore = process.memoryUsage();

    // Store original end function
    const originalEnd = res.end.bind(res);

    res.end = function (chunk?: any, encoding?: any) {
      const memAfter = process.memoryUsage();
      const memDiff = {
        rss: memAfter.rss - memBefore.rss,
        heapTotal: memAfter.heapTotal - memBefore.heapTotal,
        heapUsed: memAfter.heapUsed - memBefore.heapUsed,
        external: memAfter.external - memBefore.external,
      };

      // Record memory usage metrics
      MetricsService.recordMetric("request_memory_delta_rss", memDiff.rss, "bytes", {
        method: req.method,
        path: req.route?.path || req.path,
      });

      MetricsService.recordMetric("request_memory_delta_heap", memDiff.heapUsed, "bytes", {
        method: req.method,
        path: req.route?.path || req.path,
      });

      // Log significant memory increases
      if (memDiff.heapUsed > 10 * 1024 * 1024) {
        // 10MB
        Logger.warn(`High memory usage for request: ${req.method} ${req.path}`, {
          memoryDelta: {
            rss: `${Math.round(memDiff.rss / 1024 / 1024)}MB`,
            heapUsed: `${Math.round(memDiff.heapUsed / 1024 / 1024)}MB`,
          },
        });
      }

      return originalEnd(chunk, encoding);
    };

    next();
  };
};

// Business metrics tracking
export const trackBusinessMetric = (metricName: string, value: number = 1, tags?: Record<string, string>) => {
  MetricsService.recordMetric(`business_${metricName}`, value, "count", tags);
  Logger.business(metricName, tags?.userId, { value, tags });
};

// Custom performance timer
export class PerformanceTimer {
  private startTime: number;
  private startHrTime: bigint;
  private name: string;
  private tags: Record<string, string> | undefined;

  constructor(name: string, tags?: Record<string, string>) {
    this.name = name;
    this.tags = tags;
    this.startTime = Date.now();
    this.startHrTime = process.hrtime.bigint();
  }

  end(): number {
    const duration = Number(process.hrtime.bigint() - this.startHrTime) / 1000000; // Convert to ms

    MetricsService.recordMetric(`custom_timer_${this.name}`, duration, "ms", this.tags);

    Logger.performance(`Timer ${this.name}`, duration, this.tags);

    return duration;
  }
}

// Middleware to add performance headers
export const performanceHeaders = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    const startTime = process.hrtime.bigint();

    // Store original end function
    const originalEnd = res.end.bind(res);

    res.end = function (chunk?: any, encoding?: any) {
      const duration = Number(process.hrtime.bigint() - startTime) / 1000000; // Convert to ms

      // Add performance headers
      res.set("X-Response-Time", `${duration.toFixed(2)}ms`);
      res.set("X-Process-ID", process.pid.toString());

      if (process.env.NODE_ENV === "development") {
        const memUsage = process.memoryUsage();
        res.set("X-Memory-Usage", `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
      }

      return originalEnd(chunk, encoding);
    };

    next();
  };
};
